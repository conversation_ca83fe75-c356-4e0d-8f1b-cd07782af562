import dotenv from "dotenv";
import axios from "axios";
import { v4 as uuidv4 } from "uuid";
import fs from "fs";

import portalDb from "../../config/clinics.collections.config.js";
import { OPConsultRecordSchema } from "../../schema/fhir_schema/op_consult.schema.fhir.js";
import { PrescriptionRecordSchema } from "../../schema/fhir_schema/prescription.schema.fhir.js";
import { HealthDocumentRecordSchema } from "../../schema/fhir_schema/health_document.schema.fhir.js";

import { AbhaResponse } from "../../utils/abha.api.js";
import { DIAGNOSTIC_REPORT_RECORD, DISCHARGE_SUMMARY_RECORD, HEALTH_DOCUMENT_RECORD, IMMUNIZATION_RECORD, OP_CONSULT_RECORD, PRESCRIPTION_RECORD, WELLNESS_RECORD, INVOICE_RECORD, bundleMetadata, bundleIdentifier, signatureConstant, DIAGNOSTIC_REPORT_MEDIA_RECORD } from "../../utils/fhir.constants.js";
import { generatePatientResource } from "./common_resources/patient.resource.fhir.js";
import { generateEncounterResource } from "./common_resources/encounter.resource.fhir.js";
import { generateConditionResource } from "./common_resources/condition.resource.fhir.js";
import { generatePractitionerResource } from "./common_resources/practitioner.resource.fhir.js";
import { generateOrganizationResource } from "./common_resources/organization.resource.fhir.js";
import { generateAllergyIntoleranceResource } from "./common_resources/allergy_intolerance.resource.fhir.js";
import { generateServiceRequestResource } from "./op_consult_record/service_request.resource.fhir.js";
import { generateMedicationStatementResource } from "./common_resources/medication_statement.resource.fhir.js";
import { generateMedicationRequestResource } from "./common_resources/medication_request.resource.fhir.js";
import { generateProcedureResource } from "./common_resources/procedure.resource.fhir.js";
import { generateDocumentReferenceResource } from "./common_resources/document_reference.resource.fhir.js";
import { generateAppointmentResource } from "./op_consult_record/appointment.resource.fhir.js";
import { generateDiagnosticReportComposition, generateHealthDocumentComposition, generateImmunizationComposition, generateInvoiceComposition, generateOpConsultComposition, generateWellnessComposition } from "./common_resources/composition.resource.fhir.js";
import { generatePrescriptionComposition } from "./common_resources/composition.resource.fhir.js";
import { generateBinaryResource } from "./common_resources/binary.resource.fhir.js";
import { generateObservationResource } from "./common_resources/observation.resource.fhir.js";
import { DischargeSummaryRecordSchema } from "../../schema/fhir_schema/discharge_summary.schema.fhir.js";
import { generateImmunizationResource } from "./immunization_record/immunization.resource.fhir.js";
import { generateImmunizationRecommendationResource } from "./immunization_record/immunization_recommendation.resource.fhir.js"
import { generateDiagnosticReportResource } from "./common_resources/diagnostic_report_lab.resource.fhir.js";
import { generateInvoiceResource } from "./common_resources/invoice.resource.fhir.js";
import { generateChargeItemResource } from "./common_resources/chargeItem.resource.fhir.js";
import { ImmunizationRecordSchema } from "../../schema/fhir_schema/immunization.schema.fhir.js";
import { InvoiceRecordSchema } from "../../schema/fhir_schema/invoice.schema.fhir.js";
import { WellnessRecordSchema } from "../../schema/fhir_schema/wellness.schema.fhir.js";
import { DiagnosticReportSchema } from "../../schema/fhir_schema/diagnostic_report_lab.schema.fhir.js";
import path from "path";

dotenv.config();

export const handleApisComingFromClinicForFhir = async (req) => {
  try {
    // console.log(`Received request from Clinic in path: ${req.url}`);
    // console.log("Request Body:", req.body);

    const { general } = req.body
    const artifactKey = extractArtifactsKey(general.artifact);
    const handler = artifactHandlers[artifactKey] || artifactHandlers.defaultHandler;
    console.log('Update to: ', artifactKey);
    return await handler(req);
  } catch (error) {
    console.error("Error handling event:", error.message);
    throw error;
  }
}
export const handleApisComingFromAbhaAppForFhir = async (data) => {
  try {
    // console.log(`Received datauest from Clinic in path: ${data.url}`);
    // console.log("datauest Body:", data.body);
    const { general } = data.body
    const artifactKey = extractArtifactsKey(general.artifact);
    console.log(artifactKey)
    const handler = artifactHandlersForAbhaApp[artifactKey] || artifactHandlersForAbhaApp.defaultHandler;
    console.log('Update to: ', artifactKey);

    // if(artifactKey==='PrescriptionRecord')
    //     return fhirPrescriptionBundle = await forwardToPrescriptionRecordBundle(data, PRESCRIPTION_RECORD, getCurrentTimeInGMT530());
    // if(artifactKey==='HealthDocumentRecord')
    //     return fhirHealthDocumentBundle = await forwardToHealthDocumentRecordBundle(data, HEALTH_DOCUMENT_RECORD, getCurrentTimeInGMT530());
    // if(artifactKey==='WellnessRecord')
    //     return fhirWellnessBundle = await forwardToWellnessRecordBundle(data, WELLNESS_RECORD, getCurrentTimeInGMT530());
    // if(artifactKey==='InvoiceRecord')
    //     return fhirInvoiceBundle = await forwardToInvoiceRecordBundle(data, INVOICE_RECORD, getCurrentTimeInGMT530());
    // if(artifactKey==='OPConsultRecord')
    //     return fhirOPConsultBundle = await forwardToOPConsultRecordBundle(data, OP_CONSULT_RECORD, getCurrentTimeInGMT530());
    // if(artifactKey==='DiagnosticReportRecord')
    //     return fhirDiagnosticBundle = await forwardToDiagnosticReportRecordBundle(data, DIAGNOSTIC_REPORT_RECORD, getCurrentTimeInGMT530());
    // if(artifactKey==='ImmunizationRecord')
    //     return fhirImmunizationBundle = await forwardToImmunizationRecordBundle(data, IMMUNIZATION_RECORD, getCurrentTimeInGMT530());
    // if(artifactKey==='DischargeSummaryRecord')
    //     return fhirDischargeSummaryBundle = await forwardToDischargeSummaryRecordBundle(data, DISCHARGE_SUMMARY_RECORD, getCurrentTimeInGMT530());

    // console.log("data",data)

    return await handler(data);
  } catch (error) {
    console.error("Error handling event:", error.message);
    throw error;
  }
}

function extractArtifactsKey(artifact) {
  if (artifact.toLowerCase().includes(DIAGNOSTIC_REPORT_RECORD.toLowerCase())) return 'generateDiagnosticReportRecordBundle';
  if (artifact.toLowerCase().includes(DISCHARGE_SUMMARY_RECORD.toLowerCase())) return 'generateDischargeSummaryRecordBundle';
  if (artifact.toLowerCase().includes(HEALTH_DOCUMENT_RECORD.toLowerCase())) return 'generateHealthDocumentRecordBundle';
  if (artifact.toLowerCase().includes(IMMUNIZATION_RECORD.toLowerCase())) return 'generateImmunizationRecordBundle';
  if (artifact.toLowerCase().includes(OP_CONSULT_RECORD.toLowerCase())) return 'generateOPConsultRecordBundle';
  if (artifact.toLowerCase().includes(PRESCRIPTION_RECORD.toLowerCase())) return 'generatePrescriptionRecordBundle';
  if (artifact.toLowerCase().includes(WELLNESS_RECORD.toLowerCase())) return 'generateWellnessRecordBundle';
  if (artifact.toLowerCase().includes(INVOICE_RECORD.toLowerCase())) return 'generateInvoiceRecordBundle';
  return 'defaultHandler';
}

const artifactHandlers = {


  generateDiagnosticReportRecordBundle: async (req) => {
    try {
      const fhirId = uuidv4();

      if (req.query.skipFhirProcessing) {

        const DiagnosticReportModel = portalDb.model("DiagnosticReportFHIRRecord", DiagnosticReportSchema);
        const body = req.body;
        const diagnosticReport = new DiagnosticReportModel({
          fhirId,
          general: body.general,
          patient: body.patient,
          encounter: body.encounter,
          practitioners: body.practitioners,
          organization: body.organization,
          diagnosticReports: body.diagnosticReports,
          signature: body.signature
        });

        let result = await diagnosticReport.save();
        return new AbhaResponse(true, { bundle: result, fhirId });
      }
      const fhirDiagnosticBundle = await forwardToDiagnosticReportRecordBundle(req, DIAGNOSTIC_REPORT_RECORD, getCurrentTimeInGMT530());

      // const compositionEntry = fhirDiagnosticBundle.entry.find(entry => entry.resource.resourceType === "Composition");
      // if (!compositionEntry) {
      //     throw new Error("No Composition resource found in the FHIR bundle");
      // }
      // const compositionId = compositionEntry.resource.id;
      // // console.log("Diagnostic Report FHIR Bundle: ", JSON.stringify(fhirDiagnosticBundle, null, 2));
      // // console.log("Record saved successfully:", fhirId);

      return new AbhaResponse(true, { bundle: fhirDiagnosticBundle, fhirId });
    } catch (error) {
      console.error(error);
      return new AbhaResponse(false, {
        message: "An error occurred while processing the request",
        error: error,
      });
    }
  },

  generateDischargeSummaryRecordBundle: async (req) => {
    try {
      const fhirId = uuidv4();
      if (req.query.skipFhirProcessing) {

        const DischargeSummaryRecordModel = portalDb.model("DischargeSummaryFHIRRecord", DischargeSummaryRecordSchema);
        const body = req.body;
        const record = new DischargeSummaryRecordModel({
          fhirId,
          general: body.general,
          patient: body.patient,
          practitioners: body.practitioners,
          encounter: body.encounter,
          organization: body.organization,
          conditions: body.conditions,
          serviceRequests: body.serviceRequests,
          medicationStatements: body.medicationStatements,
          medicationRequests: body.medicationRequests,
          procedures: body.procedures,
          appointment: body.appointment,
          signature: body.signature,
          dischargeSummary: body.dischargeSummary
        });

        let result = await record.save();
        return new AbhaResponse(true, { bundle: result, fhirId });
      } else {
        const fhirDischargeSummaryBundle = await forwardToDischargeSummaryRecordBundle(req, DISCHARGE_SUMMARY_RECORD, getCurrentTimeInGMT530());

        // const compositionEntry = fhirDischargeSummaryBundle.entry.find(entry => entry.resource.resourceType === "Composition");
        // if (!compositionEntry) {
        //     throw new Error("No Composition resource found in the FHIR bundle");
        // }
        // const compositionId = compositionEntry.resource.id;
        // const fhirId = `${compositionId}`;
        return new AbhaResponse(true, { bundle: fhirDischargeSummaryBundle, fhirId });
      }
    } catch (error) {
      console.error(error);
      return new AbhaResponse(false, {
        message: "An error occurred while processing the request",
        error: error,
      });
    }
  },


  generateHealthDocumentRecordBundle: async (req) => {
    try {

      const fhirId = uuidv4();
      if (req.query.skipFhirProcessing) {
        const HealthDocumentRecordModel = portalDb.model("HealthDocumentFHIRRecord", HealthDocumentRecordSchema);

        const body = req.body;
        const healthDocument = new HealthDocumentRecordModel({
          fhirId,
          general: body.general,
          patient: body.patient,
          practitioners: body.practitioners,
          organization: body.organization,
          documentReferences: body.documentReferences,
          signature: body.signature
        });
        let result = await healthDocument.save();

        return new AbhaResponse(true, { bundle: result, fhirId });
      } else {
        const fhirHealthDocumentBundle = await forwardToHealthDocumentRecordBundle(req, HEALTH_DOCUMENT_RECORD, getCurrentTimeInGMT530());

        // const compositionEntry = fhirHealthDocumentBundle.entry.find(entry => entry.resource.resourceType === "Composition");
        // if (!compositionEntry) {
        //     throw new Error("No Composition resource found in the FHIR bundle");
        // }
        // const compositionId = compositionEntry.resource.id;
        // const fhirId = `${compositionId}`;
        return new AbhaResponse(true, { bundle: fhirHealthDocumentBundle, fhirId });
      }
    } catch (error) {
      console.error(error);
      return new AbhaResponse(false, {
        message: "An error occurred while processing the request",
        error: error,
      });
    }
  },

  generateImmunizationRecordBundle: async (req) => {
    try {
      const fhirId = uuidv4();
      if (req.query.skipFhirProcessing) {
        const ImmunizationRecordModel = portalDb.model("ImmunizationReportFHIRRecord", ImmunizationRecordSchema);
        const body = req.body;
        const immunization = new ImmunizationRecordModel({
          fhirId,
          general: body.general,
          patient: body.patient,
          encounter: body.encounter,
          practitioners: body.practitioners,
          organization: body.organization,
          immunizations: body.immunizations,
          signature: body.signature
        });

        let result = await immunization.save();
        return new AbhaResponse(true, { bundle: result, fhirId });
      } else {
        const fhirImmunizationBundle = await forwardToImmunizationRecordBundle(req, IMMUNIZATION_RECORD, getCurrentTimeInGMT530());

        // const compositionEntry = fhirImmunizationBundle.entry.find(entry => entry.resource.resourceType === "Composition");
        // if (!compositionEntry) {
        //     throw new Error("No Composition resource found in the FHIR bundle");
        // }
        // const compositionId = compositionEntry.resource.id;
        // const fhirId = `${compositionId}`;
        return new AbhaResponse(true, { bundle: fhirImmunizationBundle, fhirId });
      }
    } catch (error) {
      console.error(error);
      return new AbhaResponse(false, {
        message: "An error occurred while processing the request",
        error: error,
      });
    }
  },



  generateOPConsultRecordBundle: async (req) => {
    try {

      const fhirId = uuidv4();
      if (req.query.skipFhirProcessing) {
        const OPConsultRecordModel = portalDb.model("OPConsultFHIRRecord", OPConsultRecordSchema);
        const body = req.body;
        const opConsult = new OPConsultRecordModel({
          fhirId,
          general: body.general,
          patient: body.patient,
          practitioners: body.practitioners,
          encounter: body.encounter,
          organization: body.organization,
          conditions: body.conditions,
          serviceRequests: body.serviceRequests,
          medicationStatements: body.medicationStatements,
          medicationRequests: body.medicationRequests,
          procedures: body.procedures,
          documentReferences: body.documentReferences,
          appointment: body.appointment,
          signature: body.signature
        });
        let result = await opConsult.save();
        return new AbhaResponse(true, { bundle: result, fhirId });
      }
      else {
        const fhirOPConsultBundle = await forwardToOPConsultRecordBundle(req, OP_CONSULT_RECORD, getCurrentTimeInGMT530());

        // const compositionEntry = fhirOPConsultBundle.entry.find(entry => entry.resource.resourceType === "Composition");
        // if (!compositionEntry) {
        //     throw new Error("No Composition resource found in the FHIR bundle");
        // }
        // const compositionId = compositionEntry.resource.id;
        // const fhirId = `${compositionId}`;
        return new AbhaResponse(true, { bundle: fhirOPConsultBundle, fhirId });
      }

    } catch (error) {
      console.error(error);
      return new AbhaResponse(false, {
        message: "An error occurred while processing the request",
        error: error,
      });
    }
  },

  generatePrescriptionRecordBundle: async (req) => {
    try {

      const fhirId = uuidv4();
      if (req.query.skipFhirProcessing) {
        const PrescriptionRecordModel = portalDb.model("PrescriptionFHIRRecord", PrescriptionRecordSchema);
        const body = req.body;
        const prescription = new PrescriptionRecordModel({
          fhirId,
          general: body.general,
          patient: body.patient,
          encounter: body.encounter,
          practitioners: body.practitioners,
          conditions: body.conditions,
          organization: body.organization,
          medicationRequests: body.medicationRequests,
          binary: body.binary,
          signature: body.signature
        });
        let result = await prescription.save();
        return new AbhaResponse(true, { bundle: result, fhirId });
      }
      else {
        const fhirPrescriptionBundle = await forwardToPrescriptionRecordBundle(req, PRESCRIPTION_RECORD, getCurrentTimeInGMT530());
        return new AbhaResponse(true, { bundle: fhirPrescriptionBundle, fhirId });
      }

    } catch (error) {
      console.error(error);
      return new AbhaResponse(false, {
        message: "An error occurred while processing the request",
        error: error,
      });
    }
  },

  generateWellnessRecordBundle: async (req) => {
    try {
      const fhirId = uuidv4();
      if (req.query.skipFhirProcessing) {
        const WellnessRecordModel = portalDb.model("WellnessReportFHIRRecord", WellnessRecordSchema);
        const body = req.body;
        const wellnessRecord = new WellnessRecordModel({
          fhirId,
          general: body.general,
          patient: body.patient,
          practitioners: body.practitioners,
          // encounter: body.encounter,
          // organization: body.organization,
          observations: body.observations,
          signature: body.signature
        });
        let result = await wellnessRecord.save();
        return new AbhaResponse(true, { bundle: result, fhirId });
      }
      else {
        const fhirWellnessBundle = await forwardToWellnessRecordBundle(req, WELLNESS_RECORD, getCurrentTimeInGMT530());
        return new AbhaResponse(true, { bundle: fhirWellnessBundle, fhirId });
      }
    } catch (error) {
      console.error(error);
      return new AbhaResponse(false, {
        message: "An error occurred while processing the request",
        error: error,
      });
    }
  },

  generateInvoiceRecordBundle: async (req) => {
    try {
      const fhirId = uuidv4();
      if (req.query.skipFhirProcessing) {
        const InvoiceRecordModel = portalDb.model("InvoiceReportFHIRRecord", InvoiceRecordSchema);
        const body = req.body;
        const invoice = new InvoiceRecordModel({
          fhirId,
          general: body.general,
          patient: body.patient,
          practitioners: body.practitioners,
          encounter: body.encounter,
          organization: body.organization,
          invoice: body.invoice,
          chargeItems: body.chargeItems,
          signature: body.signature
        });
        let result = await invoice.save();
        return new AbhaResponse(true, { bundle: result, fhirId });
      } else {
        const fhirInvoiceBundle = await forwardToInvoiceRecordBundle(req, INVOICE_RECORD, getCurrentTimeInGMT530());
        return new AbhaResponse(true, { bundle: fhirInvoiceBundle, fhirId });
      }

    } catch (error) {
      console.error(error);
      return new AbhaResponse(false, {
        message: "An error occurred while processing the request",
        error: error,
      });
    }
  },


  defaultHandler: async () => {
    throw {
      status: 404,
      data: { error: { message: "Unsupported Artifact" } },
    };
  }
}
const artifactHandlersForAbhaApp = {


  generateDiagnosticReportRecordBundle: async (req) => {
    try {
      const fhirDiagnosticBundle = await forwardToDiagnosticReportRecordBundle(req, DIAGNOSTIC_REPORT_RECORD, getCurrentTimeInGMT530());
      return fhirDiagnosticBundle;

    } catch (error) {
      console.error(error);
      return new AbhaResponse(false, {
        message: "An error occurred while processing the request",
        error: error,
      });
    }
  },

  generateDischargeSummaryRecordBundle: async (req) => {
    try {
      const fhirDischargeSummaryBundle = await forwardToDischargeSummaryRecordBundle(req, DISCHARGE_SUMMARY_RECORD, getCurrentTimeInGMT530());
      return fhirDischargeSummaryBundle;

      // const filePath = path.join("./", "fhirDischargeBundle.json");
      // fs.writeFileSync(filePath, JSON.stringify(fhirDischargeSummaryBundle, null, 2), "utf-8");
      // return new AbhaResponse(true, {bundle:fhirDischargeSummaryBundle,fhirId});
    } catch (error) {
      console.error(error);
      return new AbhaResponse(false, {
        message: "An error occurred while processing the request",
        error: error,
      });
    }
  },


  generateHealthDocumentRecordBundle: async (req) => {
    try {
      const fhirHealthDocumentBundle = await forwardToHealthDocumentRecordBundle(req, HEALTH_DOCUMENT_RECORD, getCurrentTimeInGMT530());
      return fhirHealthDocumentBundle;



      // console.log("OP Consult Record FHIR Bundle: ", JSON.stringify(fhirHealthDocumentBundle, null, 2));
      // console.log("Record saved successfully:", fhirId);
      // const filePath = path.join("./", "fhirHealthDocBundle.json");

      // fs.writeFileSync(filePath, JSON.stringify(fhirHealthDocumentBundle, null, 2), "utf-8");
      // return new AbhaResponse(true, {bundle:fhirHealthDocumentBundle, fhirId});
    } catch (error) {
      console.error(error);
      return new AbhaResponse(false, {
        message: "An error occurred while processing the request",
        error: error,
      });
    }
  },

  generateImmunizationRecordBundle: async (req) => {
    try {
      const fhirImmunizationBundle = await forwardToImmunizationRecordBundle(req, IMMUNIZATION_RECORD, getCurrentTimeInGMT530());
      return fhirImmunizationBundle;


      // console.log("Immunization Record FHIR Bundle: ", JSON.stringify(fhirImmunizationBundle, null, 2));
      // console.log("Record saved successfully:", fhirId);

      // return new AbhaResponse(true, {bundle:fhirImmunizationBundle,fhirId});
    } catch (error) {
      console.error(error);
      return new AbhaResponse(false, {
        message: "An error occurred while processing the request",
        error: error,
      });
    }
  },



  generateOPConsultRecordBundle: async (req) => {
    try {
      const fhirOPConsultBundle = await forwardToOPConsultRecordBundle(req, OP_CONSULT_RECORD, getCurrentTimeInGMT530());
      return fhirOPConsultBundle;


      // console.log("OP Consult Record FHIR Bundle: ", JSON.stringify(fhirOPConsultBundle, null, 2));
      // // console.log("Record saved successfully:", fhirId);

      // return new AbhaResponse(true, {bundle:fhirOPConsultBundle,fhirId});
    } catch (error) {
      console.error(error);
      return new AbhaResponse(false, {
        message: "An error occurred while processing the request",
        error: error,
      });
    }
  },

  generatePrescriptionRecordBundle: async (req) => {
    try {
      const fhirPrescriptionBundle = await forwardToPrescriptionRecordBundle(req, PRESCRIPTION_RECORD, getCurrentTimeInGMT530());
      return fhirPrescriptionBundle;


      // console.log("Prescription Record FHIR Bundle: ", JSON.stringify(fhirPrescriptionBundle, null, 2));
      // console.log("Record saved successfully:", fhirId);
      // const filePath = path.join("./", "fhirPrescriptionBundle.json");


      // // Save JSON data to file
      // fs.writeFileSync(filePath, JSON.stringify(fhirPrescriptionBundle, null, 2), "utf-8");
      // return new AbhaResponse(true, {bundle:fhirPrescriptionBundle,fhirId});
    } catch (error) {
      console.error(error);
      return new AbhaResponse(false, {
        message: "An error occurred while processing the request",
        error: error,
      });
    }
  },

  generateWellnessRecordBundle: async (req) => {
    try {
      const fhirWellnessBundle = await forwardToWellnessRecordBundle(req, WELLNESS_RECORD, getCurrentTimeInGMT530());
      return fhirWellnessBundle;

      // const filePath = path.join("./", "fhirWellnessBundle.json");


      // Save JSON data to file
      // fs.writeFileSync(filePath, JSON.stringify(fhirWellnessBundle, null, 2), "utf-8");
      // console.log("Wellness Record FHIR Bundle: ", JSON.stringify(fhirWellnessBundle, null, 2));
      // console.log("Record saved successfully:", fhirId);

      // return new AbhaResponse(true, {bundle:fhirWellnessBundle,fhirId:fhirId});
    } catch (error) {
      console.error(error);
      return new AbhaResponse(false, {
        message: "An error occurred while processing the request",
        error: error,
      });
    }
  },

  generateInvoiceRecordBundle: async (req) => {
    try {
      const fhirInvoiceBundle = await forwardToInvoiceRecordBundle(req, INVOICE_RECORD, getCurrentTimeInGMT530());
      return fhirInvoiceBundle;


      // console.log("__dirname:", __dirname);
      // const filePath = path.join("./", "fhirInvoiceBundle.json");


      // Save JSON data to file
      // fs.writeFileSync(filePath, JSON.stringify(fhirInvoiceBundle, null, 2), "utf-8");
      // console.log("Invoice Record FHIR Bundle: ", JSON.stringify(fhirInvoiceBundle, null, 2));
      // console.log("Record saved successfully:", fhirId);

      // return new AbhaResponse(true, {bundle:fhirInvoiceBundle,fhirId});
    } catch (error) {
      console.error(error);
      return new AbhaResponse(false, {
        message: "An error occurred while processing the request",
        error: error,
      });
    }
  },


  defaultHandler: async () => {
    throw {
      status: 404,
      data: { error: { message: "Unsupported Artifact" } },
    };
  }
}

const getCurrentTimeInGMT530 = () => {
  const now = new Date();
  const offset = 5.5 * 60;
  const timezoneDate = new Date(now.getTime() + offset * 60 * 1000);
  return timezoneDate.toISOString().replace('Z', '+05:30');
};

let patientResource;
let encounterResource;
let conditionResources = [];
let practitionerResources = [];
let organizationResource;
let allergyIntoleranceResources = [];
let serviceRequestResources = [];
let medicationStatementResources = [];
let medicationRequestResources = [];
let procedureResources = [];
let documentReferenceResources = [];
let dischargeSummaryResources = [];

let immunizationRecommendationResources = [];
let immunizationResources = [];
let observationResources = [];
let diagnosticReportResources = [];
let chargeItemResources = [];
let appointmentResource;
let binaryResource;
let invoiceResources;

const clearArrayResources = async () => {
  conditionResources.length = 0;
  allergyIntoleranceResources.length = 0;
  serviceRequestResources.length = 0;
  medicationStatementResources.length = 0;
  medicationRequestResources.length = 0;
  procedureResources.length = 0;
  documentReferenceResources.length = 0;
  practitionerResources.length = 0;
}


const forwardToInvoiceRecordBundle = async (req, bundleId, currentTime) => {
  try {
    const filePath = path.join("./", "izzyBundleAfter.json");
    fs.writeFileSync(
      filePath,
      JSON.stringify(req.body, null, 2),
      "utf-8"
    );
    clearArrayResources();

    const {
      general,
      signature,
      patient,
      practitioners,
      encounter,
      organization,
      invoice,
      chargeItems,
      binary,
    } = req.body;

    patientResource = await generatePatientResource(currentTime, patient);

    for (const practitioner of practitioners) {
      practitionerResources.push(
        await generatePractitionerResource(
          currentTime,
          practitioner,
          patient.doctors
        )
      );
    }

    organizationResource = await generateOrganizationResource(organization);

    for (const chargeItem of chargeItems) {
      chargeItemResources.push(
        await generateChargeItemResource(
          chargeItem.type,
          chargeItem,
          patientResource,
          practitionerResources
        )
      );
    }
    console.log(chargeItemResources);
    invoiceResources = await generateInvoiceResource(
      invoice,
      patientResource,
      practitionerResources,
      organizationResource,
      chargeItemResources,
      currentTime
    );
    console.log(`has binary ${binary ? "Yes" : "No"}`)
    binaryResource = await generateBinaryResource(binary);

    encounterResource = await generateEncounterResource(
      currentTime,
      patientResource,
      [],
      encounter,
      [],
      general,
      patient.id
    );

    const entry = [
      await generateInvoiceComposition(
        general,
        currentTime,
        patientResource,
        patient.doctors,
        encounterResource,
        practitionerResources,
        invoiceResources,
        chargeItemResources,
        binaryResource
      ),
      patientResource,
      organizationResource,
      encounterResource,
      ...practitionerResources,
      invoiceResources,
      ...chargeItemResources,
      binaryResource
    ];

    return await generateFhirBundle(
      entry,
      currentTime,
      bundleId,
      general,
      signature
    );
  } catch (error) {
    throw error;
  }
};




const forwardToDiagnosticReportRecordBundle = async (req, bundleId, currentTime) => {
  try {
    clearArrayResources();

    const { general, signature, patient, practitioners, encounter, organization, diagnosticReports, documentReferences } = req.body;

    patientResource = await generatePatientResource(currentTime, patient);

    for (const practitioner of practitioners) {
      practitionerResources.push(await generatePractitionerResource(currentTime, practitioner, patient.doctors));
    }

    organizationResource = await generateOrganizationResource(organization);

    encounterResource = await generateEncounterResource(currentTime, patientResource, [], encounter, [], general, patient.id);
    for (const diagnosticReport of diagnosticReports) {
      diagnosticReportResources.push(await generateDiagnosticReportResource(diagnosticReport, patientResource, practitionerResources, organizationResource, diagnosticReport.categories, diagnosticReport.type, currentTime, encounterResource));
    }

    for (const documentReference of documentReferences) {
      documentReferenceResources.push(await generateDocumentReferenceResource(documentReference.status, documentReference.docStatus, documentReference.type, documentReference.content, patientResource));
    }



    const entry = [
      await generateDiagnosticReportComposition(
        general,
        currentTime,
        patientResource,
        patient.doctors,
        encounterResource,
        practitionerResources,
        organizationResource,
        diagnosticReportResources,
        documentReferenceResources
      ),
      patientResource,
      organizationResource,
      encounterResource,
      ...practitionerResources,
      ...diagnosticReportResources,
      ...documentReferenceResources
    ];

    return await generateFhirBundle(entry, currentTime, bundleId, general, signature);
  } catch (error) {
    throw error;
  }
};


const forwardToImmunizationRecordBundle = async (req, bundleId, currentTime) => {
  try {
    clearArrayResources();

    const { general, signature, patient, practitioners, encounter, organization, immunizations, documentReferences } = req.body;

    patientResource = await generatePatientResource(currentTime, patient);

    for (const practitioner of practitioners) {
      practitionerResources.push(await generatePractitionerResource(currentTime, practitioner, patient.doctors));
    }

    organizationResource = await generateOrganizationResource(organization);

    for (const immunization of immunizations) {
      immunizationResources.push(await generateImmunizationResource(immunization.type, immunization, patientResource, patient.id));
    }

    // for (const recommendation of immunizationRecommendations) {
    //     immunizationRecommendationResources.push(await 
    //         generateImmunizationRecommendationResource(recommendation.type,recommendation, patientResource, organizationResource));
    // }
    // for (const documentReference of documentReferences) {
    //     documentReferenceResources.push(await generateDocumentReferenceResource(documentReference.status, documentReference.docStatus, documentReference.type, documentReference.content, patientResource));
    // }

    encounterResource = await generateEncounterResource(currentTime, patientResource, [], encounter, [], general, patient.id);

    const entry = [
      await generateImmunizationComposition(
        general,
        currentTime,
        patientResource,
        patient.doctors,
        encounterResource,
        practitionerResources,
        organizationResource,
        immunizationResources,
        immunizationRecommendationResources,
        documentReferenceResources,
      ),
      patientResource,
      organizationResource,
      encounterResource,
      ...practitionerResources,
      ...immunizationResources,
      ...immunizationRecommendationResources,
      ...documentReferenceResources,
    ];

    return await generateFhirBundle(entry, currentTime, bundleId, general, signature);
  } catch (error) {
    throw error;
  }
};


const forwardToDischargeSummaryRecordBundle = async (req, bundleId, currentTime) => {
  try {
    await clearArrayResources();

    const { general, signature, conditions, patient, practitioners, organization, encounter, serviceRequests, medicationStatements, medicationRequests, procedures, dischargeSummary, appointment } = req.body;

    patientResource = await generatePatientResource(currentTime, patient);

    for (const practitioner of practitioners) {
      practitionerResources.push(await generatePractitionerResource(currentTime, practitioner, patient.doctors))
    }

    organizationResource = await generateOrganizationResource(organization);

    for (const allergyIntolerance of patient.allergyIntolerances) {
      allergyIntoleranceResources.push(await generateAllergyIntoleranceResource(allergyIntolerance.type, allergyIntolerance.clinicalStatus, allergyIntolerance.verificationStatus, allergyIntolerance.notes, currentTime, patientResource, practitionerResources, allergyIntolerance.doctor));
    }

    for (const serviceRequest of serviceRequests) {
      serviceRequestResources.push(await generateServiceRequestResource(serviceRequest.status, serviceRequest.intent, serviceRequest.categories, serviceRequest.type, currentTime, patientResource, practitionerResources, patient.doctors));
    }

    for (const medicationStatement of medicationStatements) {
      medicationStatementResources.push(await generateMedicationStatementResource(medicationStatement.status, medicationStatement.type, currentTime, patientResource));
    }

    for (const procedure of procedures) {
      procedureResources.push(await generateProcedureResource(procedure.status, procedure.type, procedure.performedDateTime, procedure.followUp, patientResource, patient.id));
    }

    for (const documentReference of dischargeSummary) {
      dischargeSummaryResources.push(await generateDocumentReferenceResource(documentReference.status, documentReference.docStatus, documentReference.type, documentReference.content, patientResource));
    }

    for (const cond of conditions) {
      conditionResources.push(await generateConditionResource(cond.type, cond.status, patientResource, cond));
    }

    encounterResource = await generateEncounterResource(currentTime, patientResource, conditionResources, encounter, conditions, general, patient.id);

    for (const medicationRequest of medicationRequests) {
      medicationRequestResources.push(await generateMedicationRequestResource(medicationRequest.status, medicationRequest.intent, medicationRequest.authoredOn, medicationRequest.medication, medicationRequest.forCondition, medicationRequest.reason, medicationRequest.dosageInstruction, patientResource, practitionerResources, conditionResources, patient.doctors));
    }

    appointmentResource = await generateAppointmentResource(appointment, patientResource, practitionerResources, serviceRequestResources, conditionResources, patient.doctors)

    const entry = [
      await generateOpConsultComposition(
        general,
        currentTime,
        patientResource,
        patient.doctors,
        encounterResource,
        practitionerResources,
        organizationResource,
        allergyIntoleranceResources,
        conditionResources,
        serviceRequestResources,
        medicationStatementResources,
        medicationRequestResources,
        procedureResources,
        dischargeSummaryResources,
        appointmentResource
      ),
      ...practitionerResources,
      organizationResource,
      patientResource,
      encounterResource,
      ...allergyIntoleranceResources,
      appointmentResource,
      ...conditionResources,
      ...procedureResources,
      ...serviceRequestResources,
      ...medicationStatementResources,
      ...medicationRequestResources,
      ...dischargeSummaryResources,
    ];

    return await generateFhirBundle(entry, currentTime, bundleId, general, signature);
  } catch (error) {
    throw error;
  }
};

const forwardToOPConsultRecordBundle = async (req, bundleId, currentTime) => {
  try {
    await clearArrayResources();

    const { general, signature, conditions, patient, practitioners, organization, encounter, serviceRequests, medicationStatements, medicationRequests, procedures, documentReferences, appointment } = req.body;

    patientResource = await generatePatientResource(currentTime, patient);

    for (const practitioner of practitioners) {
      practitionerResources.push(await generatePractitionerResource(currentTime, practitioner, patient.doctors))
    }


    organizationResource = await generateOrganizationResource(organization);

    for (const allergyIntolerance of patient.allergyIntolerances) {
      allergyIntoleranceResources.push(await generateAllergyIntoleranceResource(allergyIntolerance.type, allergyIntolerance.clinicalStatus, allergyIntolerance.verificationStatus, allergyIntolerance.notes, currentTime, patientResource, practitionerResources, allergyIntolerance.doctor));
    }

    for (const serviceRequest of serviceRequests) {
      serviceRequestResources.push(await generateServiceRequestResource(serviceRequest.status, serviceRequest.intent, serviceRequest.categories, serviceRequest.type, currentTime, patientResource, practitionerResources, patient.doctors, patient.id));
    }

    for (const medicationStatement of medicationStatements) {
      medicationStatementResources.push(await generateMedicationStatementResource(medicationStatement.status, medicationStatement.type, currentTime, patientResource));
    }

    for (const procedure of procedures) {
      procedureResources.push(await generateProcedureResource(procedure.status, procedure.type, procedure.performedDateTime, procedure.followUp, patientResource, patient.id));
    }

    for (const documentReference of documentReferences) {
      documentReferenceResources.push(await generateDocumentReferenceResource(documentReference.status, documentReference.docStatus, documentReference.type, documentReference.content, patientResource));
    }

    for (const cond of conditions) {
      conditionResources.push(await generateConditionResource(cond.type, cond.status, patientResource, cond, patient.id));
    }

    encounterResource = await generateEncounterResource(currentTime, patientResource, conditionResources, encounter, conditions, general, patient.id);

    for (const medicationRequest of medicationRequests) {
      medicationRequestResources.push(await generateMedicationRequestResource(medicationRequest.status, medicationRequest.intent, medicationRequest.authoredOn, medicationRequest.medication, medicationRequest.forCondition, medicationRequest.reason, medicationRequest.dosageInstruction, patientResource, practitionerResources, conditionResources, patient.doctors, patient.id));
    }

    appointmentResource = await generateAppointmentResource(appointment, patientResource, practitionerResources, serviceRequestResources, conditionResources, patient.doctors)

    const entry = [
      await generateOpConsultComposition(
        general,
        currentTime,
        patientResource,
        patient.doctors,
        encounterResource,
        practitionerResources,
        organizationResource,
        allergyIntoleranceResources,
        conditionResources,
        serviceRequestResources,
        medicationStatementResources,
        medicationRequestResources,
        procedureResources,
        documentReferenceResources,
        appointmentResource
      ),
      ...practitionerResources,
      organizationResource,
      patientResource,
      encounterResource,
      ...allergyIntoleranceResources,
      appointmentResource,
      ...conditionResources,
      ...procedureResources,
      ...serviceRequestResources,
      ...medicationStatementResources,
      ...medicationRequestResources,
      ...documentReferenceResources,
    ];

    return await generateFhirBundle(entry, currentTime, bundleId, general, signature);
  } catch (error) {
    throw error;
  }
};

const forwardToPrescriptionRecordBundle = async (req, bundleId, currentTime) => {
  try {
    clearArrayResources();

    const { general, signature, patient, practitioners, conditions, encounter, organization, medicationRequests, binary } = req.body;

    patientResource = await generatePatientResource(currentTime, patient);

    for (const practitioner of practitioners) {
      practitionerResources.push(await generatePractitionerResource(currentTime, practitioner, patient.doctors))
    }

    organizationResource = await generateOrganizationResource(organization);

    for (const cond of conditions) {
      conditionResources.push(await generateConditionResource(cond.type, cond.status, patientResource, cond, patient.id));
    }

    for (const medicationRequest of medicationRequests) {
      medicationRequestResources.push(await generateMedicationRequestResource(medicationRequest.status, medicationRequest.intent, medicationRequest.authoredOn, medicationRequest.medication, medicationRequest.forCondition, medicationRequest.reason, medicationRequest.dosageInstruction, patientResource, practitionerResources, conditionResources, patient.doctors, patient.id));
    }

    encounterResource = await generateEncounterResource(currentTime, patientResource, conditionResources, encounter, conditions, general, patient.id);

    binaryResource = await generateBinaryResource(binary);

    const entry = [
      await generatePrescriptionComposition(
        general,
        currentTime,
        patientResource,
        patient.doctors,
        encounterResource,
        practitionerResources,
        organizationResource,
        conditionResources,
        medicationRequestResources,
        binaryResource
      ),
      patientResource,
      organizationResource,
      encounterResource,
      ...practitionerResources,
      ...conditionResources,
      ...medicationRequestResources,
      binaryResource
    ];

    return await generateFhirBundle(entry, currentTime, bundleId, general, signature);
  } catch (error) {
    throw error;
  }
};



const forwardToHealthDocumentRecordBundle = async (req, bundleId, currentTime) => {
  try {
    await clearArrayResources();

    const { general, signature, patient, practitioners, organization, documentReferences } = req.body;

    patientResource = await generatePatientResource(currentTime, patient);

    for (const practitioner of practitioners) {
      practitionerResources.push(await generatePractitionerResource(currentTime, practitioner, patient.doctors))
    }

    organizationResource = await generateOrganizationResource(organization);

    for (const documentReference of documentReferences) {
      documentReferenceResources.push(await generateDocumentReferenceResource(documentReference.status, documentReference.docStatus, documentReference.type, documentReference.content, patientResource));
    }

    const entry = [
      await generateHealthDocumentComposition(
        general,
        currentTime,
        patientResource,
        patient.doctors,
        practitionerResources,
        organizationResource,
        documentReferenceResources
      ),
      ...practitionerResources,
      organizationResource,
      patientResource,
      ...documentReferenceResources,
    ]

    return await generateFhirBundle(entry, currentTime, bundleId, general, signature);
  } catch (error) {
    throw error;
  }
};

const forwardToWellnessRecordBundle = async (req, bundleId, currentTime) => {
  try {
    clearArrayResources();

    const { general, signature, patient, practitioners, observations, doctor } = req.body;

    patientResource = await generatePatientResource(currentTime, patient);
    for (const practitioner of practitioners) {
      practitionerResources.push(await generatePractitionerResource(currentTime, practitioner, patient.doctors))
    }

    // practitionerResources = await generatePractitionerResource(currentTime, practitioner,patient.doctors);
    for (const obs of observations) {
      observationResources.push(await generateObservationResource(obs, currentTime, patientResource, practitionerResources, patient.doctors));
    }

    const entry = [
      await generateWellnessComposition(
        general,
        currentTime,
        patientResource,
        practitionerResources,
        observationResources
      ),
      patientResource,
      ...practitionerResources,
      ...observationResources
    ];

    return await generateFhirBundle(entry, currentTime, bundleId, general, signature);
  } catch (error) {
    throw error;
  }
};

const generateFhirBundle = async (entry, currentTime, bundleId, general, signature) => {
  const whichResource = entry.find(resource => resource.resource?.resourceType?.toLowerCase() === signature.who.type.toLowerCase());
  const id = uuidv4();
  return {
    resourceType: 'Bundle',
    id: id,
    meta: bundleMetadata(currentTime),
    identifier: bundleIdentifier(general.hipUrl, general.clientId),
    type: 'document',
    timestamp: currentTime,
    entry,
    signature: {
      type: signatureConstant(),
      when: currentTime,
      who: whichResource ? {
        reference: `urn.:${whichResource.resource.id}`,
        display: whichResource.resource.resourceType
      } : null,
      sigFormat: signature.sigFormat,
      data: signature.data
    }
  };
}