{"_id": "683b474bb74c039d9279c913", "fhirId": "32f1f7de-4d82-4a01-a6a4-5125d497527f", "general": {"artifact": "InvoiceRecord", "hipUrl": "https://www.healtether.com", "hipIds": ["hip1", "hip2"], "status": "final", "clientId": "SBX_003515", "_id": "683b474bb74c039d9279c914"}, "patient": {"id": "68316144694ae2caa0a0c41d", "abhaNumber": "91-1248-5708-0632", "abhaAddress": "monika_12200305@sbx", "name": {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "prefix": ["undefined"]}, "gender": "female", "dob": "2003-12-05", "doctors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "allergyIntolerances": [], "telecom": [{"system": "phone", "value": "**********", "use": "mobile", "_id": "683b474bb74c039d9279c916"}], "address": [{"use": "home", "type": "physical", "text": "nimma ji ji kho , b<PERSON><PERSON> mandi gate, jiwajiganj, Gird, Gird, Gwalior, Madhya Pradesh", "city": "GWALIOR", "state": "MADHYA PRADESH", "district": "MADHYA PRADESH", "postalCode": "474001", "country": "india", "_id": "683b474bb74c039d9279c917"}], "_id": "683b474bb74c039d9279c915"}, "practitioners": [{"names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "licenses": [{"code": "MD", "display": "Medical License number", "licNo": "1234567", "_id": "683b474bb74c039d9279c919"}], "patient": "patient123", "gender": "female", "birthDate": "2025-05-31", "address": [{"use": "home", "type": "physical", "text": "<PERSON><PERSON><PERSON> ganj", "postalCode": "474001", "country": "india", "_id": "683b474bb74c039d9279c91a"}], "telecom": [{"system": "phone", "value": "**********", "use": "mobile", "_id": "683b474bb74c039d9279c91b"}], "_id": "683b474bb74c039d9279c918"}], "encounter": {"status": "finished", "startTime": "1999-12-01T10:00:00+05:30", "endTime": "2023-11-01T10:00:00+05:30", "_id": "683b474bb74c039d9279c91c"}, "organization": {"name": "TEST", "telecom": [{"system": "phone", "value": "**********", "use": "work", "_id": "683b474bb74c039d9279c91e"}], "licenses": [{"code": "PRN", "display": "Provider number", "licNo": "1234567", "_id": "683b474bb74c039d9279c91f"}], "_id": "683b474bb74c039d9279c91d"}, "invoice": {"id": "68384afe08cdf07d7ab8150b", "status": "issued", "date": "2025-05-31", "totalNet": {"currency": "INR"}, "totalGross": {"currency": "INR"}, "lineItem": [{"type": "consultation", "priceComponent": [{"type": "base", "amount": {"value": 0, "currency": "INR"}}, {"type": "discount", "amount": {"value": 0, "currency": "INR"}}, {"type": "tax", "display": "CGST", "amount": {"value": 0, "currency": "INR"}}, {"type": "tax", "display": "SGST", "amount": {"value": 0, "currency": "INR"}}]}], "_id": "683b474bb74c039d9279c920"}, "chargeItems": [{"id": "68384afe08cdf07d7ab8150c", "type": "consultation", "status": "billed", "quantity": 1, "_id": "683b474bb74c039d9279c921"}], "signature": {"who": {"type": "Practitioner", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sigFormat": "image/jpeg", "data": "c2lnbmF0dXJlIGRhdGEgaGVyZQ==", "_id": "683b474bb74c039d9279c922"}, "abhaCareContextLinked": false, "__v": 0}